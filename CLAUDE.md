# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## High-level Code Architecture and Structure

This repository contains a single HTML file (`index.html`) that functions as a client-side data visualization tool. It uses external libraries loaded via CDN for its core functionalities.

- **`index.html`**: The main and only file, containing all HTML structure, CSS styling (Tailwind CSS via CDN and inline `<style>` tags), and JavaScript logic.

### Key Libraries/Frameworks Used:
- **Tailwind CSS**: A utility-first CSS framework for styling the application.
- **Chart.js**: A JavaScript charting library used for rendering various types of interactive data visualizations (bar, line, pie, doughnut, radar charts).
- **SheetJS (xlsx.full.min.js)**: A library for reading and parsing Excel (.xlsx, .xls) and CSV (.csv) files uploaded by the user.
- **Font Awesome**: Used for icons throughout the application.

### Core Application Flow:
1.  **Data Upload**: Users can upload Excel or CSV files via drag-and-drop or a file browser. The application parses the file content.
2.  **Data Preview**: A portion of the uploaded data is displayed in a table for user verification.
3.  **Visualization Configuration**: Users select chart types (bar, line, pie, etc.), map data columns to X and Y axes, set chart titles, choose color themes, and toggle legend/grid visibility.
4.  **Chart Generation**: Based on user selections, an interactive chart is generated using Chart.js.
5.  **Chart Export**: The generated chart can be exported as an image (PNG, JPG). Placeholders for SVG and PDF export exist, indicating potential future enhancements requiring additional libraries.

### Development Environment:
This project is a static web application. Development primarily involves direct edits to `index.html` and viewing changes in a web browser. There are no build scripts, test runners, or linting tools configured in this repository.

## Common Development Tasks

Since this is a single HTML file, common development tasks involve directly modifying the `index.html` file.

-   **Running the application**: Open `index.html` in any web browser.
-   **Making changes**: Edit `index.html` and refresh the browser to see updates.
-   **Adding new features**: Implement new functionalities directly within the `<script>` tag in `index.html` or by adding new HTML elements and Tailwind classes.
-   **Debugging**: Use browser developer tools to inspect elements, debug JavaScript, and view console logs.
